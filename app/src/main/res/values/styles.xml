<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Custom Button Styles -->

    <!-- Primary Button Style -->
    <style name="CustomButton.Primary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="strokeWidth">2dp</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
    </style>

    <!-- Primary Button Filled Style -->
    <style name="CustomButton.Primary.Filled" parent="Widget.Material3.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
    </style>

    <!-- Error Button Style -->
    <style name="CustomButton.Error" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="strokeWidth">2dp</item>
        <item name="strokeColor">?attr/colorError</item>
        <item name="android:textColor">?attr/colorError</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">?attr/colorError</item>
    </style>

    <!-- Text Button Style -->
    <style name="CustomButton.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
    </style>

    <!-- Small Button Style -->
    <style name="CustomButton.Small" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">36dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">4dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
</resources>
